# 第五章：信号发生器数学基础专题（复合信号发生器版）

## 📚 学习目标
- 理解复合信号发生器中各种信号变换的数学原理
- 掌握积分器、滤波器等运放电路的数学模型
- 学会用数学方法分析20kHz/5kHz双频信号系统
- 能够进行频域分析和仿真验证
- 理解信号叠加和滤波的数学基础

---

## 5.1 复合信号发生器数学模型概述

### 5.1.1 系统信号流数学描述

**信号变换链：**
```
20kHz方波 → 四分频 → 5kHz方波 → 积分 → 5kHz三角波
                         ↓
                    5kHz方波 → 加法器 ← 5kHz三角波
                         ↓
                    复合信号 → 滤波器 → 5kHz正弦波
```

**数学表达式：**
```
v₁(t) = 方波(20kHz)                    # 施密特触发器输出
v₂(t) = 方波(5kHz) = v₁(t/4)          # 四分频输出
v₃(t) = ∫v₂(t)dt                      # 积分器输出（三角波）
v₄(t) = v₂(t) + v₃(t)                 # 加法器输出（复合信号）
v₅(t) = H(jω)·V₄(jω)                  # 滤波器输出（正弦波）
```

### 5.1.2 频域特性分析

**基本频率关系：**
```
f₁ = 20kHz    (基础振荡频率)
f₂ = 5kHz     (四分频后频率)
f₃ = 5kHz     (三角波频率)
f₄ = 5kHz     (复合信号基频)
f₅ = 5kHz     (正弦波频率)
```

**频谱分析：**
- 20kHz方波：基频20kHz + 奇次谐波(60kHz, 100kHz, ...)
- 5kHz方波：基频5kHz + 奇次谐波(15kHz, 25kHz, ...)
- 5kHz三角波：基频5kHz + 奇次谐波(15kHz, 25kHz, ...)，幅度∝1/n²
- 复合信号：两种波形的频谱叠加
- 5kHz正弦波：主要为5kHz基频，高次谐波被滤除

---

## 5.2 积分器的数学模型

### 5.2.1 理想积分器数学描述

**微分方程：**
```
vₒᵤₜ(t) = -1/(RC) ∫vᵢₙ(t)dt + vₒᵤₜ(0)
```

**传递函数：**
```
H(s) = Vₒᵤₜ(s)/Vᵢₙ(s) = -1/(RCs)
```

**频域特性：**
```
H(jω) = -1/(jωRC) = -1/(ωRC) ∠-90°
|H(jω)| = 1/(ωRC)
∠H(jω) = -90°
```

### 5.2.2 5kHz方波积分的数学分析

**输入信号（5kHz方波）：**
```
vᵢₙ(t) = A·sgn(sin(2π·5000·t))
其中：A = 1V（幅度），T = 200μs（周期）
```

**积分过程分析：**
```
在正半周期（0 < t < T/2 = 100μs）：
vᵢₙ(t) = +A
vₒᵤₜ(t) = -A/(RC)·t + C₁

在负半周期（T/2 < t < T = 200μs）：
vᵢₙ(t) = -A  
vₒᵤₜ(t) = +A/(RC)·(t-T/2) + C₂
```

**三角波输出：**
```
时间常数：τ = RC = 10kΩ × 3.3nF = 33μs
斜率：±A/τ = ±1V/33μs ≈ ±30.3V/ms
峰值：A·T/(4τ) = 1V × 200μs/(4×33μs) ≈ 1.52V
```

**实际设计考虑：**
```
为获得1Vpp输出，需要增益调节：
Av = -Rf/R = -2.2kΩ/10kΩ = -0.22
实际输出：1.52V × 0.22 ≈ 0.33V → 需要输入幅度调节
```

### 5.2.3 积分器频率响应

**幅频特性：**
```
|H(f)| = 1/(2πfRC)
在5kHz：|H(5kHz)| = 1/(2π×5000×33×10⁻⁶) ≈ 0.96
在15kHz：|H(15kHz)| = 1/(2π×15000×33×10⁻⁶) ≈ 0.32
在25kHz：|H(25kHz)| = 1/(2π×25000×33×10⁻⁶) ≈ 0.19
```

**相频特性：**
```
∠H(f) = -90°（所有频率）
积分器对所有频率分量都产生-90°相移
```

---

## 5.3 滤波器的传递函数分析

### 5.3.1 二阶Sallen-Key滤波器

**电路拓扑：**
```
复合信号 → R₁ → R₂ → 运放(+) → 正弦波输出
              ↓    ↓
             C₁   C₂
              ↓    ↓
             GND  运放(-)
```

**传递函数推导：**
```
设：R₁ = R₂ = R = 3.3kΩ，C₁ = C₂ = C = 10nF
传递函数：H(s) = K/(s²RC + 2sRC + 1)
其中：K = 增益系数
```

**标准二阶形式：**
```
H(s) = K·ωₙ²/(s² + 2ζωₙs + ωₙ²)
其中：
ωₙ = 1/(RC) = 1/(3.3kΩ×10nF) ≈ 30303 rad/s
fₙ = ωₙ/(2π) ≈ 4.82kHz
ζ = 1（临界阻尼）
```

### 5.3.2 滤波器频率响应

**幅频特性：**
```
|H(jω)| = K/√[(1-(ω/ωₙ)²)² + (2ζω/ωₙ)²]

在5kHz（ω/ωₙ ≈ 1.04）：
|H(j2π×5000)| ≈ K/√[(1-1.04²)² + (2×1×1.04)²] ≈ K/2.1 ≈ 0.48K

在15kHz（ω/ωₙ ≈ 3.11）：
|H(j2π×15000)| ≈ K/√[(1-3.11²)² + (2×1×3.11)²] ≈ K/9.7 ≈ 0.10K
```

**相频特性：**
```
∠H(jω) = -arctan(2ζω/ωₙ/(1-(ω/ωₙ)²))

在5kHz：∠H ≈ -arctan(2.08/(-0.08)) ≈ -92.2°
在15kHz：∠H ≈ -arctan(6.22/(-8.67)) ≈ 144.3°
```

### 5.3.3 谐波抑制分析

**输入复合信号频谱：**
```
基频(5kHz)：幅度A₁
三次谐波(15kHz)：幅度A₃ ≈ A₁/9（三角波特性）
五次谐波(25kHz)：幅度A₅ ≈ A₁/25
```

**滤波后输出：**
```
基频：A₁ × 0.48K = 0.48KA₁
三次谐波：A₃ × 0.10K = 0.10K × A₁/9 ≈ 0.011KA₁
五次谐波：A₅ × 0.06K = 0.06K × A₁/25 ≈ 0.002KA₁
```

**总谐波失真(THD)：**
```
THD = √[(0.011KA₁)² + (0.002KA₁)²]/0.48KA₁ ≈ 2.3%
满足<3%的设计要求 ✓
```

---

## 5.4 信号叠加的数学基础

### 5.4.1 同相加法器数学模型

**电路方程：**
```
设两个输入：v₁(t) = 5kHz方波，v₂(t) = 5kHz三角波
输入电阻：R₁ = R₂ = 10kΩ
反馈电阻：Rf = 10kΩ
```

**传递函数：**
```
vₒᵤₜ(t) = (1 + Rf/R₁) × (v₁(t)×R₂ + v₂(t)×R₁)/(R₁+R₂)
        = (1 + 1) × (v₁(t) + v₂(t))/2
        = v₁(t) + v₂(t)
```

### 5.4.2 时域叠加分析

**5kHz方波：**
```
v₁(t) = A₁·sgn(sin(2π×5000×t))，A₁ = 1V
```

**5kHz三角波：**
```
v₂(t) = A₂·tri(2π×5000×t)，A₂ = 1V
其中tri(x)为三角波函数
```

**复合信号：**
```
vₒᵤₜ(t) = v₁(t) + v₂(t)
在正半周期：vₒᵤₜ(t) = 1 + A₂·tri(ωt)
在负半周期：vₒᵤₜ(t) = -1 + A₂·tri(ωt)
峰值：±(1 + A₂) = ±2V
```

### 5.4.3 频域叠加分析

**方波频谱：**
```
V₁(jω) = (4A₁/π) × Σ[sin(nωt)/n]，n = 1,3,5,...
基频：4A₁/π ≈ 1.27A₁
三次谐波：4A₁/(3π) ≈ 0.42A₁
```

**三角波频谱：**
```
V₂(jω) = (8A₂/π²) × Σ[sin(nωt)/n²]，n = 1,3,5,...
基频：8A₂/π² ≈ 0.81A₂
三次谐波：8A₂/(9π²) ≈ 0.09A₂
```

**复合信号频谱：**
```
Vₒᵤₜ(jω) = V₁(jω) + V₂(jω)
基频：1.27A₁ + 0.81A₂ ≈ 2.08V（A₁=A₂=1V时）
三次谐波：0.42A₁ + 0.09A₂ ≈ 0.51V
```

---

## 5.5 频域分析方法

### 5.5.1 傅里叶级数分析

**周期信号的傅里叶展开：**
```
f(t) = a₀/2 + Σ[aₙcos(nω₀t) + bₙsin(nω₀t)]
其中：ω₀ = 2π/T = 2π×5000 = 31416 rad/s
```

**方波的傅里叶级数：**
```
方波(t) = (4A/π) × Σ[sin(nω₀t)/n]，n = 1,3,5,...
= (4A/π)[sin(ω₀t) + sin(3ω₀t)/3 + sin(5ω₀t)/5 + ...]
```

**三角波的傅里叶级数：**
```
三角波(t) = (8A/π²) × Σ[sin(nω₀t)/n²]，n = 1,3,5,...
= (8A/π²)[sin(ω₀t) + sin(3ω₀t)/9 + sin(5ω₀t)/25 + ...]
```

### 5.5.2 系统频率响应分析

**整体系统传递函数：**
```
H_total(jω) = H_integrator(jω) × H_adder(jω) × H_filter(jω)
            = [-1/(jωRC)] × [1] × [K·ωₙ²/(jω)² + 2ζωₙ(jω) + ωₙ²]
```

**5kHz处的系统响应：**
```
|H_total(j2π×5000)| = [1/(2π×5000×33×10⁻⁶)] × 1 × 0.48K
                    ≈ 0.96 × 0.48K ≈ 0.46K
```

### 5.5.3 相位关系分析

**各级相位：**
```
积分器：∠H₁ = -90°
加法器：∠H₂ = 0°
滤波器：∠H₃ ≈ -92.2°（5kHz处）
总相位：∠H_total ≈ -90° + 0° - 92.2° = -182.2°
```

**相位对波形的影响：**
- 积分器：方波→三角波，相位滞后90°
- 滤波器：复合信号→正弦波，相位滞后92.2°
- 总体：输出正弦波相对输入方波滞后182.2°

---

## 5.6 仿真验证方法

### 5.6.1 SPICE仿真模型

**READ2302G运放模型：**
```
.model READ2302G opamp(
+ Rin=1e12 Rout=20 Avd=100000
+ GBW=6e6 SR=8e6 Vos=3e-3
+ Ib=1e-12 Ios=2e-12
+ Vcc=5 Vee=0
)
```

**仿真电路网表示例：**
```
* 20kHz振荡器
R1 n1 n2 10k
R2 n2 n3 2.2k  
C1 n2 0 4.7n
X1 n3 n2 n1 READ2302G

* 积分器
R3 n4 n5 10k
C2 n5 n6 3.3n
R4 n5 n6 2.2k
X2 vbias n5 n6 READ2302G
```

### 5.6.2 数值仿真方法

**差分方程离散化：**
```
积分器：y[n] = y[n-1] + (Δt/RC)×x[n]
其中：Δt = 采样间隔，建议Δt < T/100 = 2μs
```

**MATLAB仿真代码框架：**
```matlab
% 参数设置
fs = 1e6;           % 采样频率1MHz
dt = 1/fs;          % 采样间隔
t = 0:dt:1e-3;      % 仿真时间1ms
f1 = 20e3;          % 20kHz
f2 = 5e3;           % 5kHz

% 信号生成
v1 = square(2*pi*f1*t);        % 20kHz方波
v2 = square(2*pi*f2*t);        % 5kHz方波（四分频后）
v3 = cumsum(v2)*dt/(RC);       % 积分器输出
v4 = v2 + v3;                  % 加法器输出
v5 = filter(num,den,v4);       % 滤波器输出
```

### 5.6.3 实验验证方法

**关键测试点：**
```
1. 频率测量：用频率计测量各级输出频率
2. 幅度测量：用示波器测量峰峰值
3. 波形观察：用示波器观察波形形状
4. 相位测量：用双通道示波器测量相位差
5. 频谱分析：用频谱仪分析谐波含量
```

**误差分析：**
```
频率误差：Δf/f = ΔRC/RC + 温度漂移 + 负载影响
幅度误差：ΔA/A = 运放增益误差 + 电源纹波 + 负载影响
相位误差：Δφ = 运放相位滞后 + RC网络相移 + 布线寄生
```

---

## 📖 本章小结

**数学模型要点：**
1. **积分器模型：** H(s) = -1/(RCs)，实现方波到三角波转换
2. **滤波器模型：** 二阶低通，fₙ≈4.82kHz，THD<3%
3. **加法器模型：** 线性叠加，vₒᵤₜ = v₁ + v₂
4. **频域分析：** 傅里叶级数，谐波分析，系统传递函数

**关键数学关系：**
1. **时间常数：** τ = RC = 33μs，决定积分器特性
2. **截止频率：** fc = 1/(2πRC) ≈ 4.82kHz，决定滤波特性
3. **相位关系：** 积分器-90°，滤波器-92.2°，总计-182.2°
4. **幅度关系：** 各级增益精确计算，确保输出幅度

**仿真验证：**
1. **SPICE仿真：** 电路级精确仿真
2. **数值仿真：** MATLAB/Python系统级仿真
3. **实验验证：** 示波器、频谱仪测量验证
4. **误差分析：** 理论值与实测值对比分析

**实用价值：**
- 为电路设计提供理论依据
- 为参数选择提供计算方法
- 为故障分析提供数学工具
- 为性能优化提供理论指导

**下一章预告：**
第六章将学习复合信号发生器专用的快速索引和检查清单，包括5分钟快速搭建指南、故障排除对照表、参数验证清单等实用工具。
