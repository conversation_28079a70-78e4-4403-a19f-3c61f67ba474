# 第二章：标准电路模板库（复合信号发生器专用）

## 📚 学习目标
- 掌握READ2302G双运放在复合信号发生器中的应用
- 学会设计20kHz方波发生器和5kHz三角波发生器
- 掌握HD74LS74四分频器的设计和应用
- 学会设计同相加法器和低通滤波器
- 能够快速选择和应用复合信号发生器专用电路模板
- 理解+5V单电源下的600Ω负载驱动设计

---

## 2.1 复合信号发生器系统架构

### 2.1.1 信号流程图
```
20kHz方波 ----→ 四分频器 ----→ 5kHz方波
产生器           (HD74LS74)        |
                                  ↓
                              三角波产生器
                              (积分器)
                                  |
                                  ↓
                              5kHz三角波
                                  |
                                  ↓
                              同相加法器 ←---- 5kHz方波
                                  |
                                  ↓
                              复合信号
                                  |
                                  ↓
                              低通滤波器
                                  |
                                  ↓
                              5kHz正弦波
```

### 2.1.2 系统参数要求
| 模块 | 频率 | 幅度 | 负载阻抗 | 失真度 |
|------|------|------|----------|--------|
| 20kHz方波 | 20kHz±100Hz | 3V±5% | 600Ω | <5% |
| 5kHz方波 | 5kHz±100Hz | 1V±5% | 600Ω | <5% |
| 5kHz三角波 | 5kHz±100Hz | 1V±5% | 600Ω | <5% |
| 复合信号 | 5kHz±100Hz | 2V±5% | 600Ω | <5% |
| 5kHz正弦波 | 5kHz±100Hz | 3V±5% | 600Ω | <3% |

### 2.1.3 电源配置
**统一电源：** +5V单电源，0V地线
**偏置电压：** +2.5V（VCC/2）
**去耦要求：** 每个READ2302G芯片VCC和GND间加0.1μF陶瓷电容

---

## 2.2 模板一：20kHz方波产生器

### 2.2.1 基于READ2302G的施密特触发振荡器

**电路图：**
```
+5V ----+----[R1=10kΩ]----+----[R2=2.2kΩ]----+
        |                  |                   |
      [0.1μF]              |                 [C1]
        |                  |                   |
       GND               Pin3                Pin2
                           |                   |
                      READ2302G(A)            |
                           |                   |
                         Pin1 ----------------+
                           |
                         [R3]
                           |
                         +2.5V偏置
```

**关键参数：**
- R1 = 10kΩ（反馈电阻）
- R2 = 2.2kΩ（充电电阻）
- C1 = 3.3nF（定时电容）
- R3 = 20kΩ（偏置电阻，两个10kΩ串联分压）

### 2.2.2 频率计算公式

**基本公式：**
```
f = 1 / (2 × R2 × C1 × ln((1+β)/(1-β)))
其中：β = R1/(R1+R2) = 10k/(10k+2.2k) ≈ 0.82
```

**20kHz设计计算：**
```
目标频率：f = 20kHz
ln((1+0.82)/(1-0.82)) = ln(1.82/0.18) ≈ 2.31
f = 1 / (2 × 2.2kΩ × C1 × 2.31)
C1 = 1 / (2 × 2.2k × 2.31 × 20000) ≈ 4.9nF
选择标准值：C1 = 4.7nF
实际频率：f ≈ 20.8kHz（在±100Hz范围内）
```

### 2.2.3 输出特性分析

**输出电压：**
- 高电平：VOH ≈ 4.9V（VDD-0.1V）
- 低电平：VOL ≈ 0.1V（VSS+0.1V）
- 输出摆幅：Vpp = 4.8V

**600Ω负载驱动：**
```
输出电流：Iout = 4.8V / 600Ω = 8mA
READ2302G最大输出电流：±30mA
驱动裕量：30mA / 8mA = 3.75倍 ✓
```

**输出缓冲器（推荐）：**
```
READ2302G ----[100Ω]----+----→ 输出
   OUT                   |
                       [600Ω]
                         |
                        GND
```

### 2.2.4 调试要点
1. **频率调节：** 微调C1值（4.7nF±10%）
2. **幅度调节：** 检查电源电压和偏置电压
3. **波形质量：** 检查上升下降时间（应<1μs）
4. **负载测试：** 接入600Ω负载验证驱动能力

---

## 2.3 模板二：HD74LS74四分频器

### 2.3.1 T触发器配置

**电路图：**
```
20kHz输入 ----→ CLK1 [HD74LS74A] Q1 ----→ CLK2 [HD74LS74B] Q2 ----→ 5kHz输出
                  |                         |
                 D1 ←------ /Q1            D2 ←------ /Q2
                  |                         |
                PRE1 ----+5V              PRE2 ----+5V
                  |                         |
                CLR1 ----+5V              CLR2 ----+5V
```

**连接说明：**
- D1连接到/Q1：构成T触发器
- D2连接到/Q2：构成T触发器
- PRE和CLR接+5V：正常工作模式
- 两级T触发器级联：实现4分频

### 2.3.2 时序分析

**输入信号要求：**
- 输入频率：20kHz±100Hz
- 输入幅度：4.8Vpp（READ2302G输出）
- 占空比：45%~55%

**输出信号特性：**
- 输出频率：5kHz±25Hz
- 输出幅度：5Vpp（TTL电平）
- 占空比：50%（理想方波）

**时序参数验证：**
```
20kHz周期：T = 50μs
建立时间要求：tsu = 25ns << 25μs ✓
保持时间要求：th = 0ns（无要求）✓
脉冲宽度要求：tw = 25ns << 25μs ✓
```

### 2.3.3 电平转换设计

**TTL到CMOS电平转换：**
```
HD74LS74 ----[1kΩ]----+----→ READ2302G输入
   Q输出                |
                      [1kΩ]
                        |
                       +2.5V偏置
```

**转换后信号特性：**
- 高电平：2.5V + 2.5V = 5V → 4.9V（限幅）
- 低电平：2.5V - 2.5V = 0V → 0.1V（限幅）
- 输出摆幅：约4.8Vpp

### 2.3.4 调试要点
1. **时钟信号：** 确保20kHz输入信号质量良好
2. **电源连接：** 检查+5V电源和地线连接
3. **复位信号：** PRE和CLR必须接+5V
4. **输出负载：** 验证TTL输出驱动能力

---

## 2.4 模板三：5kHz三角波产生器

### 2.4.1 积分器电路设计

**电路图：**
```
5kHz方波输入 ----[R1=10kΩ]----+----[-]
                                |       \
                                |        △----→ 三角波输出
                              [C1]      /
                                |      [+]----[R2=10kΩ]----+2.5V
                              输出             |
                                             [R3=10kΩ]
                                              |
                                             GND
```

**设计参数：**
- R1 = 10kΩ（积分电阻）
- C1 = 3.3nF（积分电容）
- R2 = R3 = 10kΩ（偏置分压）

### 2.4.2 传递函数推导

**积分器传递函数：**
```
H(s) = -1/(R1×C1×s)
对于方波输入，积分后得到三角波
```

**幅度计算：**
```
方波幅度：Vin = 4.8Vpp
积分时间常数：τ = R1×C1 = 10kΩ × 3.3nF = 33μs
5kHz周期：T = 200μs
三角波幅度：Vout = Vin × T/(4×τ) = 4.8V × 200μs/(4×33μs) ≈ 7.3V

实际限制：输出摆幅限制在0.1V~4.9V
实际输出：约4.8Vpp（接近满摆幅）
```

### 2.4.3 幅度调节设计

**可调增益积分器：**
```
5kHz方波 ----[R1]----+----[-]
                      |       \
                      |        △----→ 三角波输出
                    [C1]      /
                      |      [+]----[R2]----+2.5V
                    [Rf]
                      |
                    输出
```

**增益调节：**
- 增益：Av = -Rf/R1
- 选择Rf = 2.2kΩ，R1 = 10kΩ
- 增益：Av = -0.22
- 输出幅度：4.8V × 0.22 ≈ 1.06V ≈ 1V±5% ✓

### 2.4.4 调试要点
1. **偏置调节：** 确保+2.5V偏置准确
2. **积分常数：** 微调C1值调节波形线性度
3. **幅度控制：** 调节Rf/R1比值控制输出幅度
4. **波形质量：** 检查三角波的线性度和对称性

---

## 2.5 模板四：同相加法器

### 2.5.1 双输入同相加法器

**电路图：**
```
5kHz方波 ----[R1=10kΩ]----+
                            |
5kHz三角波 --[R2=10kΩ]----+----[+]
                            |       \
                          [R3]       △----→ 复合信号输出
                            |       /
                           +2.5V   [-]----[R4=10kΩ]----+
                                    |                   |
                                  [R5=10kΩ]           输出
                                    |
                                   GND
```

**设计参数：**
- R1 = R2 = 10kΩ（输入电阻）
- R4 = R5 = 10kΩ（反馈网络）
- R3 = 20kΩ（偏置电阻）

### 2.5.2 传递函数分析

**加法器传递函数：**
```
Vout = (1 + R4/R5) × (V1×R2 + V2×R1)/(R1+R2) + 偏置
     = 2 × (V1 + V2)/2 + 2.5V
     = V1 + V2 + 2.5V
```

**输出幅度计算：**
```
5kHz方波：1Vpp（经电平转换后）
5kHz三角波：1Vpp
复合信号：1V + 1V = 2Vpp ✓
直流偏置：+2.5V
总输出范围：1.5V ~ 3.5V（在0.1V~4.9V范围内）
```

### 2.5.3 电平匹配设计

**输入信号调理：**
```
HD74LS74输出 ----[分压器]----→ 1Vpp方波
三角波输出 ----[衰减器]----→ 1Vpp三角波
```

**分压器设计：**
```
5V TTL ----[R6=8.2kΩ]----+----→ 1V信号
                          |
                        [R7=2.2kΩ]
                          |
                         +2.5V
```

### 2.5.4 调试要点
1. **输入幅度：** 确保两路输入都是1Vpp
2. **相位关系：** 检查两路信号的相位关系
3. **输出幅度：** 验证输出为2Vpp
4. **直流偏置：** 检查输出直流分量为+2.5V

---

## 2.6 模板五：低通滤波器

### 2.6.1 二阶Sallen-Key滤波器

**电路图：**
```
复合信号输入 ----[R1=3.3kΩ]----+----[R2=3.3kΩ]----+----[-]
                                 |                    |       \
                               [C1=10nF]              |        △----→ 正弦波输出
                                 |                  [C2=10nF]  /
                                GND                    |      [+]----[R3]----+2.5V
                                                     输出             |
                                                                    [R4]
                                                                     |
                                                                    GND
```

**设计参数：**
- R1 = R2 = 3.3kΩ
- C1 = C2 = 10nF
- R3 = R4 = 10kΩ（偏置分压）

### 2.6.2 滤波器特性分析

**截止频率：**
```
fc = 1/(2π√(R1×R2×C1×C2))
   = 1/(2π√(3.3k×3.3k×10n×10n))
   = 1/(2π×3.3k×10n)
   ≈ 4.8kHz
```

**品质因数：**
```
Q = 0.5√(R2/R1) = 0.5（R1=R2时）
阻尼比：ζ = 1/Q = 2（过阻尼，平滑响应）
```

**5kHz处的衰减：**
```
f/fc = 5kHz/4.8kHz ≈ 1.04
二阶滤波器：-40dB/decade
5kHz处衰减：约-1dB（轻微衰减）
```

### 2.6.3 增益补偿设计

**有源滤波器增益：**
```
Av = 1 + R4/R3 = 1 + 10k/10k = 2
```

**输出幅度计算：**
```
输入：2Vpp复合信号
基波分量：约1.4Vpp（5kHz正弦分量）
滤波器增益：×2
输出：1.4V × 2 = 2.8Vpp ≈ 3V±5% ✓
```

### 2.6.4 调试要点
1. **截止频率：** 微调R1、R2或C1、C2调节截止频率
2. **增益调节：** 调节R3/R4比值控制输出幅度
3. **波形质量：** 检查正弦波的失真度（应<3%）
4. **相位响应：** 验证5kHz处的相位滞后

---

## 2.7 完整系统集成

### 2.7.1 系统连接图
```
[20kHz方波] ----→ [四分频] ----→ [电平转换] ----→ [加法器]
产生器              HD74LS74        分压器           ↑
                       |                            |
                       ↓                            |
                   [电平转换] ----→ [三角波] --------+
                     分压器          积分器
                                       |
                                       ↓
                                   [滤波器] ----→ [正弦波输出]
```

### 2.7.2 电源分配
```
+5V主电源 ----+----→ READ2302G (VCC)
              |
              +----→ HD74LS74 (VCC)
              |
              +----→ 偏置分压网络
              |
             [去耦电容0.1μF × 4个]
              |
             GND
```

### 2.7.3 测试端子配置
| 测试点 | 信号 | 预期参数 | 测试方法 |
|--------|------|----------|----------|
| TP1 | 20kHz方波 | 3V±5%, 20kHz±100Hz | 示波器+频率计 |
| TP2 | 5kHz方波 | 1V±5%, 5kHz±100Hz | 示波器+频率计 |
| TP3 | 5kHz三角波 | 1V±5%, 5kHz±100Hz | 示波器+失真仪 |
| TP4 | 复合信号 | 2V±5%, 5kHz±100Hz | 示波器+频谱仪 |
| TP5 | 5kHz正弦波 | 3V±5%, 5kHz±100Hz | 示波器+失真仪 |
| TP6 | +5V电源 | 5V±0.25V | 万用表 |

### 2.7.4 系统调试流程
1. **电源检查：** 验证+5V电源和偏置电压
2. **逐级调试：** 从20kHz方波开始，逐级验证
3. **负载测试：** 每个输出都接600Ω负载测试
4. **性能验证：** 测量频率、幅度、失真度
5. **整体联调：** 验证系统整体性能指标

---

## 📖 本章小结

**核心电路模板：**
1. **20kHz方波产生器：** 基于READ2302G的施密特触发振荡器
2. **四分频器：** HD74LS74双T触发器级联
3. **5kHz三角波产生器：** 可调增益积分器
4. **同相加法器：** 双输入同相加法器
5. **低通滤波器：** 二阶Sallen-Key有源滤波器

**设计要点：**
1. **+5V单电源：** 所有电路统一使用+5V单电源
2. **600Ω负载：** 所有输出都能驱动600Ω负载
3. **精确参数：** 所有元件参数都经过精确计算
4. **模块化设计：** 每个模块独立设计，便于调试

**实用技巧：**
1. **参数计算：** 提供完整的设计计算过程
2. **调试指导：** 每个模块都有具体的调试要点
3. **故障排除：** 预设常见问题的解决方案
4. **性能验证：** 明确的测试方法和验收标准

**下一章预告：**
第三章将学习20kHz/5kHz专用的参数计算速查表，包括RC时间常数、频率计算公式、600欧姆负载分析等实用查找表。
