# 第二章：555定时器与标准电路模板库（2021年版）

## 📚 学习目标
- 掌握TLC555CDR定时器的基本原理和应用
- 学会设计9kHz±1kHz可调方波发生器
- 掌握方波转正弦波和锯齿波的电路设计
- 能够快速选择和应用2021年专用电路模板
- 理解单电源下的电路设计要点

---

## 2.1 TLC555CDR基础理论

### 2.1.1 TLC555CDR基本特性
**TLC555CDR** 是德州仪器生产的CMOS版555定时器，专为低功耗应用设计。

**关键参数对比：**
| 参数 | TLC555CDR | NE555 | 单位 |
|------|-----------|-------|------|
| 电源电压 | 2~15 | 4.5~16 | V |
| 电源电流 | 170 | 3000 | μA |
| 输出电流 | ±100 | ±200 | mA |
| 频率稳定性 | ±50 | ±100 | ppm/°C |
| 工作温度 | -40~125 | 0~70 | °C |

**2021年题目优势：**
- 低功耗：适合+10V单电源应用
- 高精度：频率稳定性好，适合9kHz精确控制
- 宽电源范围：+10V工作完全没问题

### 2.1.2 TLC555CDR引脚定义
```
    TLC555CDR (SOIC-8)
    +---+---+---+---+
    |1  |2  |3  |4  |
    +---+---+---+---+
    |8  |7  |6  |5  |
    +---+---+---+---+

Pin 1: GND (接地)
Pin 2: TRIG (触发输入，低电平有效)
Pin 3: OUT (输出)
Pin 4: RESET (复位输入，低电平有效)
Pin 5: CTRL (控制电压，通常接0.01μF到地)
Pin 6: THRES (阈值输入)
Pin 7: DISCH (放电输出)
Pin 8: VCC (电源，接+10V)
```

### 2.1.3 工作原理
**内部结构：**
- 两个比较器：上比较器和下比较器
- RS触发器：控制输出状态
- 放电晶体管：控制电容充放电

**电压阈值（+10V电源）：**
- 上阈值：VTH = 2VCC/3 = 6.67V
- 下阈值：VTL = VCC/3 = 3.33V
- 控制电压：VCTRL = 2VCC/3 = 6.67V

---

## 2.2 9kHz可调方波发生器设计

### 2.2.1 无稳态多谐振荡器电路

**标准电路图：**
```
+10V ----+----[Ra]----+----[Rb]----+
         |             |            |
       [0.1μF]         |          [C]
         |             |            |
        GND           Pin7         Pin6/Pin2
                       |            |
                    TLC555CDR       |
                       |            |
                      Pin3 --------Pin4
                       |            |
                      OUT          +10V
                       |
                     [RL=510Ω]
                       |
                      GND
```

**工作原理：**
1. **充电过程：** 电容C通过Ra+Rb充电，当VC达到6.67V时输出翻转
2. **放电过程：** 电容C通过Rb放电，当VC降到3.33V时输出再次翻转
3. **周期性振荡：** 不断重复充放电过程

### 2.2.2 频率计算公式

**基本公式：**
```
f = 1.44 / ((Ra + 2Rb) × C)
```

**占空比：**
```
D = (Ra + Rb) / (Ra + 2Rb)
```

**9kHz设计计算：**
```
目标频率：f = 9kHz
选择C = 0.01μF（标准值）
计算：Ra + 2Rb = 1.44 / (9000 × 0.01×10⁻⁶) = 16kΩ

设计方案：
Ra = 1kΩ（调节范围用）
Rb = 7.5kΩ（可用7.5kΩ可变电阻）
验证：f = 1.44 / ((1k + 2×7.5k) × 0.01μF) = 9kHz ✓
```

### 2.2.3 可调频率设计

**±1kHz调节范围设计：**
```
频率范围：8kHz ~ 10kHz
对应电阻范围：14.4kΩ ~ 18kΩ

实用设计：
Ra = 1kΩ（固定）
Rb = 6.8kΩ + 2kΩ可变电阻
总范围：Ra + 2Rb = 14.6kΩ ~ 18.6kΩ
频率范围：7.7kHz ~ 9.9kHz（满足±1kHz要求）
```

**标准元件配置：**
- Ra：1kΩ金属膜电阻（1%）
- Rb：6.8kΩ + 2kΩ多圈电位器
- C：0.01μF NPO陶瓷电容（5%）
- 去耦电容：0.01μF（Pin5到地）

### 2.2.4 输出特性分析

**输出电压：**
- 高电平：VOH ≈ VCC - 0.1V = 9.9V
- 低电平：VOL ≈ 0.1V
- 输出摆幅：Vpp ≈ 9.8V

**负载驱动能力：**
```
负载电阻：RL = 510Ω
输出电流：Iout = 9.9V / 510Ω ≈ 19.4mA
TLC555CDR最大输出电流：±100mA
驱动裕量：100mA / 19.4mA ≈ 5.2倍 ✓
```

---

## 2.3 方波转正弦波电路

### 2.3.1 二阶低通滤波器设计

**Sallen-Key低通滤波器：**
```
方波输入 ----[R1]----+----[-]
                      |       \
                     [R2]      △----→ 正弦波输出
                      |       /
                     [C1]    [+]----[R3]----+
                      |              |      |
                     GND            [C2]   输入
                                     |
                                    GND
```

**设计参数（9kHz应用）：**
```
截止频率：fc = 9kHz
品质因数：Q = 0.707（巴特沃斯响应）
增益：Av = 1（单位增益）

元件计算：
R1 = R2 = R = 1.8kΩ
C1 = C2 = C = 0.01μF
fc = 1/(2πRC) = 1/(2π × 1.8kΩ × 0.01μF) ≈ 8.84kHz
```

### 2.3.2 简化积分器方案

**单运放积分器：**
```
方波输入 ----[R]----+----[-]
                     |       \
                     |        △----→ 三角波
                    [C]      /
                     |      [+]----[R/2]----+5V
                    输出             |
                                   [R/2]
                                    |
                                   GND
```

**9kHz设计参数：**
```
积分时间常数：τ = RC
对于9kHz方波，周期T = 111μs
选择τ = T/10 = 11μs

设计：
R = 1.1kΩ
C = 0.01μF
τ = 1.1kΩ × 0.01μF = 11μs ✓

偏置电阻：R/2 = 550Ω（提供+5V偏置）
```

### 2.3.3 输出波形分析

**理论分析：**
```
方波输入：Vin = ±5V，f = 9kHz
积分器输出：三角波，幅度 = 5V × T/(4τ)
计算：Vout_pp = 5V × 111μs/(4 × 11μs) ≈ 12.6V

实际限制：
运放输出范围：0V ~ 8.5V
实际输出幅度：约6V峰峰值
```

**谐波抑制：**
```
基波（9kHz）：衰减 = 20log(1) = 0dB
三次谐波（27kHz）：衰减 = 20log(3) ≈ -9.5dB
五次谐波（45kHz）：衰减 = 20log(5) ≈ -14dB
总谐波失真：THD < 5%
```

---

## 2.4 方波转锯齿波电路

### 2.4.1 积分器实现方案

**基本积分器电路：**
```
方波输入 ----[R]----+----[-]
                     |       \
                     |        △----→ 锯齿波输出
                    [C]      /
                     |      [+]----[10kΩ]----+5V
                   复位开关         |
                                 [10kΩ]
                                   |
                                  GND
```

**工作原理：**
1. **积分阶段：** 方波正半周时，电容充电产生上升锯齿
2. **复位阶段：** 方波负半周时，通过复位开关快速放电
3. **周期重复：** 每个方波周期产生一个锯齿波

### 2.4.2 参数设计

**时间常数设计：**
```
方波周期：T = 1/9kHz = 111μs
积分时间：Ti = T/2 = 55.5μs
时间常数：τ = RC，选择τ = Ti/5 = 11μs

设计参数：
R = 1.1kΩ
C = 0.01μF
τ = 11μs ✓
```

**输出幅度计算：**
```
输入方波幅度：Vin = 5V
积分时间：Ti = 55.5μs
输出幅度：Vout = Vin × Ti/τ = 5V × 55.5μs/11μs ≈ 25V

实际限制：运放饱和电压 ≈ 8.5V
实际输出：约4V锯齿波（受运放限制）
```

### 2.4.3 改进设计方案

**双运放方案：**
```
方波 → 积分器1 → 比较器 → 积分器2 → 锯齿波
      (慢积分)   (复位控制)  (快复位)
```

**具体参数：**
- 积分器1：R1 = 10kΩ, C1 = 0.01μF（慢积分）
- 比较器：检测积分器1输出，产生复位脉冲
- 积分器2：R2 = 1kΩ, C2 = 0.01μF（快复位）

---

## 2.5 单电源运放电路模板

### 2.5.1 单电源偏置设计

**标准偏置电路：**
```
+10V ----[10kΩ]----+----→ +5V偏置输出
                    |
                  [10kΩ]
                    |
                   GND
```

**偏置电容：**
```
+5V偏置 ----[100μF]----+----→ 稳定偏置
                       |
                     [0.1μF]
                       |
                      GND
```

### 2.5.2 单电源放大器模板

**同相放大器：**
```
输入 ----[0.1μF]----[+]
                     |  \
                     |   △----[0.1μF]----输出
                     |  /
                   [10kΩ] [-]
                     |   |
                   +5V  [Rf]
                        |
                       GND
```

**设计要点：**
- 输入耦合电容：0.1μF（截止频率 ≈ 160Hz）
- 输出耦合电容：0.1μF
- 偏置电阻：10kΩ
- 反馈电阻：根据增益要求选择

### 2.5.3 510Ω负载驱动设计

**输出缓冲器：**
```
信号输入 ----[+]
             |  \
             |   △----[0.1μF]----[510Ω]----输出
             |  /                  |
            [-]                   GND
             |
            输入
```

**驱动能力验证：**
```
最大输出电压：8.5V
负载电流：8.5V / 510Ω ≈ 16.7mA
LM324DR输出能力：±20mA
安全裕量：20mA / 16.7mA ≈ 1.2倍
结论：需要注意不要过载
```

---

## 2.6 快速选择表和参数配置

### 2.6.1 555定时器配置速查表

**9kHz±1kHz频率配置：**
| 目标频率 | Ra | Rb | C | 实际频率 | 误差 |
|---------|----|----|---|---------|------|
| 8kHz | 1kΩ | 8.5kΩ | 0.01μF | 8.0kHz | 0% |
| 9kHz | 1kΩ | 7.5kΩ | 0.01μF | 9.0kHz | 0% |
| 10kHz | 1kΩ | 6.7kΩ | 0.01μF | 10.0kHz | 0% |

**可变电阻配置：**
- 固定电阻：Ra = 1kΩ
- 可变电阻：6.8kΩ + 2kΩ电位器
- 调节范围：7.7kHz ~ 9.9kHz

### 2.6.2 波形转换电路速查表

| 输入波形 | 输出波形 | 推荐电路 | 关键参数 |
|---------|---------|---------|---------|
| 直流 | 9kHz方波 | 555振荡器 | Ra=1kΩ, Rb=7.5kΩ, C=0.01μF |
| 方波 | 正弦波 | 积分器+滤波 | R=1.1kΩ, C=0.01μF |
| 方波 | 锯齿波 | 积分器 | R=1.1kΩ, C=0.01μF |
| 方波 | 三角波 | 积分器 | R=10kΩ, C=0.01μF |

### 2.6.3 负载驱动配置表

| 负载阻抗 | 最大输出 | 输出电流 | 驱动方案 |
|---------|---------|---------|---------|
| 510Ω | 8.5V | 16.7mA | 直接驱动 |
| 1kΩ | 8.5V | 8.5mA | 直接驱动 |
| 100Ω | 2V | 20mA | 限流驱动 |
| 50Ω | 1V | 20mA | 缓冲器 |

---

## 2.7 实用设计技巧

### 2.7.1 频率精度优化
**元件选择：**
- 电阻：1%金属膜电阻
- 电容：5% NPO陶瓷电容
- 可变电阻：多圈精密电位器

**温度补偿：**
```
频率温度系数：±50ppm/°C
在±25°C范围内：频率变化 < ±0.125%
9kHz频率变化：< ±11.25Hz（满足要求）
```

### 2.7.2 负载隔离设计
**输出缓冲：**
```
555输出 → 跟随器 → 负载
优点：隔离负载影响，提高频率稳定性
缺点：增加一个运放
```

### 2.7.3 电源去耦优化
**去耦电容配置：**
- TLC555CDR：VCC到GND，0.1μF + 10μF
- LM324DR：每个运放0.1μF
- 控制端：Pin5到GND，0.01μF

---

## 📖 本章小结

**核心电路模板：**
1. **TLC555CDR振荡器：** 9kHz±1kHz可调方波发生器
2. **积分器电路：** 方波转三角波/锯齿波
3. **滤波器电路：** 方波转正弦波
4. **单电源放大器：** 信号调理和驱动

**关键参数配置：**
1. **555振荡器：** Ra=1kΩ, Rb=7.5kΩ, C=0.01μF
2. **积分器：** R=1.1kΩ, C=0.01μF, τ=11μs
3. **偏置电压：** +5V（VCC/2）
4. **负载驱动：** 510Ω，最大16.7mA

**实用设计要点：**
1. **频率精度：** 使用1%电阻和5%电容
2. **负载隔离：** 输出缓冲器设计
3. **电源去耦：** 完善的去耦电容配置
4. **温度稳定性：** TLC555CDR的优势

**下一章预告：**
第三章将制作9kHz频率参数计算速查表，提供快速查找和现场计算的实用工具。

---

## 2.8 电路实现检查清单

### ✅ TLC555CDR电路检查
- [ ] 电源连接：Pin8接+10V，Pin1接GND
- [ ] 控制端去耦：Pin5接0.01μF到GND
- [ ] 复位端连接：Pin4接+10V（正常工作）
- [ ] 输出负载：Pin3接510Ω到GND

### ✅ 频率调节检查
- [ ] Ra固定电阻：1kΩ ± 1%
- [ ] Rb可变电阻：6.8kΩ + 2kΩ电位器
- [ ] 定时电容：0.01μF ± 5% NPO
- [ ] 频率范围：8kHz ~ 10kHz

### ✅ 运放电路检查
- [ ] 电源去耦：每个运放0.1μF
- [ ] 偏置电压：+5V ± 0.1V
- [ ] 耦合电容：输入输出各0.1μF
- [ ] 负载驱动：输出电流 < 20mA

### ✅ 波形质量检查
- [ ] 方波：上升时间 < 1μs
- [ ] 正弦波：THD < 5%
- [ ] 锯齿波：线性度 > 95%
- [ ] 输出幅度：满足题目要求

---

## 2.9 故障排除指南

### 🔧 常见问题及解决方案

**问题1：555不振荡**
- 检查电源连接（Pin8, Pin1）
- 检查复位端（Pin4应接高电平）
- 检查定时元件Ra, Rb, C
- 测量控制端电压（Pin5应为6.67V）

**问题2：频率不准确**
- 检查定时电容容值
- 检查电阻阻值（用万用表测量）
- 检查电源电压稳定性
- 考虑温度影响

**问题3：输出波形失真**
- 检查负载是否过重
- 检查电源去耦电容
- 检查运放是否饱和
- 检查信号幅度是否合适

**问题4：负载驱动不足**
- 计算输出电流是否超限
- 增加输出缓冲器
- 检查电源供电能力
- 考虑使用推挽输出

---

## 2.10 性能优化建议

### 🚀 频率稳定性优化
1. **精密元件：** 使用1%电阻和5%电容
2. **温度补偿：** 选择温度系数小的元件
3. **电源稳定：** 使用稳压电源和去耦电容
4. **屏蔽设计：** 减少外界干扰

### 🚀 波形质量优化
1. **滤波设计：** 适当的低通滤波
2. **阻抗匹配：** 输出阻抗与负载匹配
3. **布线优化：** 减少寄生参数
4. **接地设计：** 良好的接地系统

### 🚀 负载驱动优化
1. **缓冲放大：** 增加输出缓冲器
2. **电流限制：** 保护电路设计
3. **多路输出：** 分配器设计
4. **隔离设计：** 避免相互影响
